# Contributing to Software Maturity Models

Thank you for your interest in contributing to the Software Maturity Models project! We welcome contributions that help improve our maturity models, enhance the technical implementation, or expand the educational content.

## License Agreement

**By submitting a pull request to this project, you agree that your contributions will be licensed under our dual-license structure:**

- **Code contributions** (source code, configuration files, technical implementation) will be licensed under the **Apache License 2.0**
- **Content contributions** (maturity models, documentation, educational materials) will be licensed under the **Creative Commons Attribution-ShareAlike 4.0 International (CC BY-SA 4.0)**

You confirm that you have the right to license your contributions under these terms and that your contributions are your original work or properly attributed third-party content that is compatible with these licenses.

## Types of Contributions

### 🔧 Code Contributions (Apache-2.0)
- Bug fixes and technical improvements
- New features for the web application
- Performance optimizations
- Build system and deployment improvements
- Testing infrastructure

### 📚 Content Contributions (CC BY-SA 4.0)
- New maturity models or dimensions
- Improvements to existing models
- Documentation enhancements
- Educational guides and examples
- Translations

### 🚫 Brand Assets (Not Accepted)
We do not accept contributions of brand assets (logos, wordmarks, etc.) as these remain under "All Rights Reserved" licensing.

## Getting Started

1. **Fork the repository** on GitHub
2. **Clone your fork** locally
3. **Create a feature branch** from `main`
4. **Make your changes** following our guidelines
5. **Test your changes** thoroughly
6. **Submit a pull request** with a clear description

## Development Setup

```bash
# Clone your fork
git clone https://github.com/YOUR-USERNAME/software-maturity-models.git
cd software-maturity-models

# Install dependencies
pnpm install

# Start development server
pnpm dev

# Run tests (if available)
pnpm test
```

## Contribution Guidelines

### For Code Changes
- Follow existing code style and conventions
- Include appropriate tests for new functionality
- Update documentation as needed
- Ensure builds pass without errors

### For Content Changes
- Follow the established maturity model structure
- Provide clear, actionable guidance for each level
- Include relevant examples and references
- Maintain consistency with existing content style

### Pull Request Process
1. Ensure your PR has a clear title and description
2. Reference any related issues
3. Include screenshots for UI changes
4. Be responsive to feedback and review comments
5. Squash commits before merging if requested

## Questions or Need Help?

- **General questions**: Open a [GitHub Discussion](https://github.com/seanblonien/software-maturity-models/discussions)
- **Bug reports**: Create an [Issue](https://github.com/seanblonien/software-maturity-models/issues)
- **Feature requests**: Create an [Issue](https://github.com/seanblonien/software-maturity-models/issues) with the "enhancement" label

## Code of Conduct

We are committed to providing a welcoming and inclusive environment for all contributors. Please be respectful, constructive, and collaborative in all interactions.

---

Thank you for contributing to the Software Maturity Models project! Your contributions help make software development practices more accessible and actionable for teams worldwide.
