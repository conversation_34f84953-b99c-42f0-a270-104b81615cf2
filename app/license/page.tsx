import { Breadcrumbs } from "@/src/components/Breadcrumbs"
import { <PERSON><PERSON><PERSON>ender<PERSON> } from "@/src/components/MarkdownRenderer"

const licenseContent = `
# License Information

This project uses a dual-license structure to appropriately cover different types of content and ensure proper attribution and sharing requirements.

## License Overview

| Content Type | License | Scope |
|--------------|---------|-------|
| **Source Code** | [Apache License 2.0](https://www.apache.org/licenses/LICENSE-2.0) | All technical implementation, configuration files, build scripts |
| **Educational Content** | [Creative Commons Attribution-ShareAlike 4.0 International](https://creativecommons.org/licenses/by-sa/4.0/) | Maturity models, documentation, guides, assessments |
| **Brand Assets** | All Rights Reserved | Logos, wordmarks, brand imagery |

## What This Means

### For Code (Apache-2.0)
- ✅ **Commercial use** - Use in commercial projects
- ✅ **Modification** - Modify and create derivative works
- ✅ **Distribution** - Distribute original or modified versions
- ✅ **Patent use** - Express grant of patent rights
- ⚠️ **Attribution required** - Include copyright notice and license
- ⚠️ **State changes** - Document modifications made

### For Content (CC BY-SA 4.0)
- ✅ **Share** - Copy and redistribute in any medium or format
- ✅ **Adapt** - Remix, transform, and build upon the material
- ✅ **Commercial use** - Use for commercial purposes
- ⚠️ **Attribution required** - Credit the creator and indicate changes
- ⚠️ **ShareAlike** - Distribute derivatives under the same license
- ⚠️ **Indicate changes** - Mark any modifications made

### For Brand Assets (All Rights Reserved)
- ❌ **No reuse** - Logos and brand assets cannot be used without permission
- ❌ **No modification** - Brand elements are protected
- 📧 **Contact required** - Reach out for permission to use brand assets

## How to Attribute

### When Using Content (Maturity Models, Documentation)

**Minimum attribution:**
> Software Maturity Models by Sean Blonien is licensed under CC BY-SA 4.0.  
> Available at: https://github.com/seanblonien/software-maturity-models

**Full attribution example:**
> This work is based on "Software Maturity Models" by Sean Blonien, licensed under CC BY-SA 4.0. The original work is available at https://github.com/seanblonien/software-maturity-models. [Describe any changes made]

### When Using Code

Follow standard Apache-2.0 attribution by including the copyright notice and license text in your distribution. See the [LICENSE](https://github.com/seanblonien/software-maturity-models/blob/main/LICENSE) file for details.

## Directory Structure & License Scope

\`\`\`
├── src/           # Source code (Apache-2.0)
├── app/           # Next.js application code (Apache-2.0)
├── components/    # React components (Apache-2.0)
├── lib/           # Utility libraries (Apache-2.0)
├── content/       # Maturity models & documentation (CC BY-SA 4.0)
├── public/brand/  # Brand assets (All Rights Reserved)
└── public/        # Other static assets (Apache-2.0)
\`\`\`

## Contributing

By contributing to this project, you agree that:
- Your **code contributions** will be licensed under Apache-2.0
- Your **content contributions** will be licensed under CC BY-SA 4.0
- You have the right to license your contributions under these terms

See [CONTRIBUTING.md](https://github.com/seanblonien/software-maturity-models/blob/main/CONTRIBUTING.md) for more details.

## Questions?

For licensing questions or permission requests regarding brand assets, please [open an issue](https://github.com/seanblonien/software-maturity-models/issues) or contact the maintainer.

---

**Full License Texts:**
- [Apache License 2.0](https://www.apache.org/licenses/LICENSE-2.0)
- [Creative Commons Attribution-ShareAlike 4.0 International](https://creativecommons.org/licenses/by-sa/4.0/)
`

export default function LicensePage() {
  return (
    <div className="space-y-6">
      <Breadcrumbs items={[{ label: "Home", href: "/" }, { label: "License" }]} />
      
      <div className="max-w-4xl">
        <MarkdownRenderer content={licenseContent} />
      </div>
    </div>
  )
}
